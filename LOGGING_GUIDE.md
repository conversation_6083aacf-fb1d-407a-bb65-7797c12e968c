# Logging Implementation Guide

## Overview

This document describes the comprehensive logging implementation for the Q-Control Backend application. The logging system provides structured, configurable logging while maintaining backward compatibility with existing code.

## Features

### 1. **Proper Python Logging Integration**

- Uses Python's built-in `logging` module
- Configurable logging levels (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- Structured log output with timestamps and context

### 2. **File-Based Logging with Rotation**

- **application.log**: General application logs
- **error.log**: Error logs only
- **debug.log**: Debug logs (only when DEBUG=True)
- **django.log**: Django framework logs
- **Automatic rotation**: Daily rotation with 30-day retention

### 3. **Backward Compatibility**

- Existing `LoggingService` class continues to work unchanged
- Console output is preserved alongside file logging
- No changes required to existing code

### 4. **Structured Logging**

- Service operation logging
- Database operation logging
- HTTP request/response logging
- Extra context data for log analysis

## Configuration

### Django Settings

The logging configuration is added to `core/settings.py`:

```python
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
            'datefmt': '%Y-%m-%d %H:%M:%S',
        },
        # ... other formatters
    },
    'handlers': {
        'file_application': {
            'level': 'INFO',
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'filename': os.path.join(BASE_DIR, 'logs', 'application.log'),
            'when': 'midnight',
            'interval': 1,
            'backupCount': 30,
            'formatter': 'verbose',
            'encoding': 'utf-8',
        },
        # ... other handlers
    },
    'loggers': {
        'reviews': {
            'handlers': ['file_application', 'file_error', 'console'],
            'level': 'DEBUG' if DEBUG else 'INFO',
            'propagate': False,
        },
        # ... other app loggers
    },
}
```

### Log Files Location

All log files are stored in the `logs/` directory:

- `logs/application.log` - General application logs
- `logs/error.log` - Error logs only
- `logs/debug.log` - Debug logs (development only)
- `logs/django.log` - Django framework logs

## Usage

### 1. **Using LoggingService (Existing Code)**

No changes required to existing code:

```python
from base.services.logging.logger import LoggingService

logger = LoggingService()
logger.log_info("This is an info message")
logger.log_error(exception, error_type="CUSTOM_ERROR")
```

### 2. **Using LoggingService with App-Specific Logger**

```python
from base.services.logging.logger import LoggingService

logger = LoggingService(logger_name='reviews')
logger.log_info("Review operation started")
logger.log_service_operation(
    service_name='ReviewsService',
    operation='create_review',
    status='success',
    details='Review ID: 123'
)
```

## New Logging Methods

### Service Operation Logging

```python
logger.log_service_operation(
    service_name='ReviewsService',
    operation='create_review',
    status='success',
    details='Review ID: 123'
)
```

### Database Operation Logging

```python
logger.log_database_operation(
    model='Review',
    operation='create',
    record_id=123,
    status='success'
)
```

### HTTP Request/Response Logging

```python
logger.log_request(request, message="Processing review creation")
logger.log_response(response, message="Review creation completed")
```

## Testing

### Check Log Files

After using the logging system, check the log files:

- `logs/application.log` - Should contain all application messages
- `logs/error.log` - Should contain error messages only
- `logs/debug.log` - Should contain debug messages (if DEBUG=True)

## Log Rotation

- **Frequency**: Daily at midnight
- **Retention**: 30 days for most logs, 7 days for debug logs
- **Naming**: Files are automatically named with date suffixes (e.g., `application.log.2025-08-04`)

## Best Practices

### 1. **Use App-Specific Loggers**

```python
# Good
logger = LoggingService(logger_name='reviews')

# Better - specify logger name explicitly
logger = LoggingService(logger_name='reviews.service')
```

### 2. **Use Structured Logging for Operations**

```python
# Good
logger.log_info("Review created")

# Better
logger.log_service_operation(
    service_name='ReviewsService',
    operation='create_review',
    status='success',
    details=f'Review ID: {review.id}, User: {user.username}'
)
```

### 3. **Include Context in Error Messages**

```python
try:
    review = Review.objects.get(id=review_id)
except Review.DoesNotExist:
    logger.log_warning(f"Review not found: ID {review_id}")
except Exception as e:
    logger.log_error(e, error_type="GET_REVIEW_ERROR")
```

### 4. **Use Structured Logging Methods**

```python
# Good
logger.log_info("Review created")

# Better
logger.log_service_operation(
    service_name='ReviewsService',
    operation='create_review',
    status='success',
    details=f'Review ID: {review.id}, User: {user.username}'
)
```

## Monitoring and Analysis

### Log Analysis

- Use tools like `grep`, `awk`, or log analysis platforms
- Structured extra data makes logs machine-readable
- Filter by service, operation, or error type

### Example Log Queries

```bash
# Find all errors in reviews service
grep "reviews.*ERROR" logs/application.log

# Find specific service operations
grep "Service: ReviewsService" logs/application.log

# Find database operations
grep "DB Operation" logs/application.log
```

## Migration Guide

### For Existing Code

No changes required! Existing `LoggingService` usage continues to work exactly as before.

### For New Code

Consider using the enhanced structured logging methods:

- `log_service_operation()` for service methods
- `log_database_operation()` for database operations
- `log_request()` and `log_response()` for HTTP operations

### Gradual Migration

You can gradually enhance existing logging by:

1. Adding app-specific logger names
2. Using structured logging methods where beneficial
3. Adding context to existing log messages

## File Structure

```
base/
├── services/
│   └── logging/
│       ├── __init__.py
│       └── logger.py          # Enhanced LoggingService
logs/
├── README.md                  # Log directory documentation
├── application.log            # General application logs
├── error.log                  # Error logs only
├── debug.log                  # Debug logs (development)
└── django.log                 # Django framework logs
```

This implementation provides a robust, scalable logging solution that enhances debugging, monitoring, and maintenance capabilities while preserving all existing functionality.
