from accounts.models import Profile, UserPreference
from base.models import Department
from base.services.logging.logger import LoggingService
from base.services.responses import APIResponse
from rest_framework import status
from django.contrib.auth import authenticate
from rest_framework_simplejwt.tokens import AccessToken, RefreshToken
from django.contrib.auth.models import User

logger = LoggingService()


class AuthService:
    def login_email_password(self, credentials) -> APIResponse:
        try:

            username = credentials.get("username")
            password = credentials.get("password")

            if username is None or password is None:
                return APIResponse(
                    success=False,
                    message="Please provide both email and password",
                    code=status.HTTP_400_BAD_REQUEST,
                )

            user = authenticate(username=username, password=password)
            if not user:

                return APIResponse(
                    success=False,
                    message="Invalid username or password",
                    code=status.HTTP_400_BAD_REQUEST,
                )

            # generate and return a JSON Web Token
            profile, department, facility, facilities, preferences = (
                self._get_user_details(user)
            )

            if not profile:
                return APIResponse(
                    success=False,
                    message="User profile not found",
                    code=status.HTTP_400_BAD_REQUEST,
                )
            if profile.is_test_account:
                access_token = AccessToken.for_user(user)
                refresh_token = RefreshToken.for_user(user)
                access_token["first_name"] = user.first_name
                access_token["last_name"] = user.last_name
                access_token["email"] = user.email
                user_info = {
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "email": user.email,
                    "id": user.id,
                    "profile_id": profile.id,
                    "department": (
                        {
                            "name": department.name,
                            "id": department.id,
                        }
                        if department
                        else None
                    ),
                    "facility": (
                        {
                            "id": facility.id,
                            "name": facility.name,
                        }
                        if facility
                        else None
                    ),
                    "accounts": [
                        {
                            "id": account.id,
                            "name": account.name,
                        }
                        for account in facilities
                        if facilities or []
                    ],
                    "phone_number": profile.phone_number if profile else None,
                    "gender": profile.gender if profile else None,
                    "preferences": {
                        "timezone": preferences.user_timezone if preferences else None,
                    },
                }
                return APIResponse(
                    success=True,
                    message="Login successful",
                    data={
                        "access": str(access_token),
                        "refresh": str(refresh_token),
                        "user_info": user_info,
                    },
                    code=status.HTTP_200_OK,
                )

            elif profile.mfa_enabled and not profile.is_test_account:
                return APIResponse(
                    success=False,
                    message="Check your authenticator app for a code",
                    code=status.HTTP_202_ACCEPTED,
                )

            elif not profile.is_test_account and not profile.mfa_enabled:
                return APIResponse(
                    success=False,
                    message="Two-factor authentication is recommended",
                    code=status.HTTP_204_NO_CONTENT,
                )

            else:
                return APIResponse(
                    success=False,
                    message="Your account is neither test or active account. Please contact admin",
                    code=status.HTTP_400_BAD_REQUEST,
                )
        except Exception as e:
            logger.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def check_access(self, data) -> APIResponse:
        username = data.get("username")
        print(f"Checking access for user: {username}")
        for user in User.objects.all():
            print(f"User: {user.username}")
            if user.username == username:
                print(f"User is here: {user.username}")
        try:
            user = User.objects.get(username=username)
            print(f"User found: {user.username}")
            profile, department, facility, facilities, preferences = (
                self._get_user_details(user)
            )
            access_token = AccessToken.for_user(user)
            refresh_token = RefreshToken.for_user(user)
            user_info = {
                "first_name": user.first_name,
                "last_name": user.last_name,
                "email": user.email,
                "department": (
                    {
                        "name": department.name,
                        "id": department.id,
                    }
                    if department
                    else None
                ),
                "facility": (
                    {
                        "id": facility.id,
                        "name": facility.name,
                    }
                    if facility
                    else None
                ),
                "accounts": [
                    {
                        "id": account.id,
                        "name": account.name,
                    }
                    for account in facilities
                    if facilities or []
                ],
                "phone_number": profile.phone_number if profile else None,
                "gender": profile.gender if profile else None,
                "preferences": {
                    "timezone": preferences.user_timezone if preferences else None,
                },
            }
            return APIResponse(
                success=True,
                message="User found",
                data={
                    "access": str(access_token),
                    "refresh": str(refresh_token),
                    "user_info": user_info,
                },
                code=status.HTTP_200_OK,
            )

        except User.DoesNotExist:
            print("User not found")
            return APIResponse(
                success=False,
                message="User not found",
                code=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:
            logger.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def _get_user_details(self, user):
        # profile
        profile = [None]
        department = None
        facility = None
        facilities = []
        preferences = None
        if user:
            try:
                profile = Profile.objects.filter(user=user).first()
                department = Department.objects.filter(members=user).first()
                facility = profile.facility if profile else None
                facilities = profile.access_to_facilities.all() if profile else []
                preferences = UserPreference.objects.get(user=user)
            except Profile.DoesNotExist:
                pass
            except UserPreference.DoesNotExist:
                pass
        return (profile, department, facility, facilities, preferences)
