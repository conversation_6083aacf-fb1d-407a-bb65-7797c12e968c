# Enhanced Activity System

This document describes the enhanced activity system that provides comprehensive activity tracking across all incident types with flexible highlight support and intelligent message generation.

## Overview

The enhanced activity system includes:

1. **ActivityLogCreateSerializer**: For creating activity logs with flexible activity_highlight support
2. **ActivityLogListSerializer**: For listing logs with the exact response structure specified
3. **ActivityLogService**: Service class for intelligent activity creation
4. **Enhanced Views**: Updated views that work with the new serializers
5. **Comprehensive Tests**: Full test coverage for all components

## Key Features

- **Polymorphic Support**: Uses Django's ContentType framework to support any incident model
- **Flexible Highlights**: Support for highlighting users, departments, or any other model
- **Intelligent Messages**: Auto-generates activity messages based on activity type and context
- **File Support**: JSON field for storing file metadata
- **Backward Compatibility**: Maintains compatibility with existing endpoints

## Usage Examples

### 1. Creating Activities with the Service

#### Document Added Activity
```python
from activities.services import ActivityLogService
from activities.models import ActivityType

files_data = [
    {
        'document_url': 'https://example.com/file.pdf',
        'document_id': 'doc123',
        'name': 'Report',
        'file_type': 'pdf'
    }
]

activity = ActivityLogService.create_activity(
    user=request.user,
    content_object=incident,
    activity_type=ActivityType.DOCUMENT_ADDED,
    files=files_data
)
# Auto-generates message: "1 incoming document uploaded"
```

#### Sent to Department Activity
```python
activity = ActivityLogService.create_activity(
    user=request.user,
    content_object=incident,
    activity_type=ActivityType.SENT_TO_DEPARTMENT,
    highlight_object=department
)
# Auto-generates message: "incident sent to Finance"
```

#### Review Added Activity
```python
activity = ActivityLogService.create_activity(
    user=request.user,
    content_object=incident,
    activity_type=ActivityType.REVIEW_ADDED,
    highlight_object=reviewer_user
)
# Auto-generates message: "review added by John Doe"
```

#### Simple Update Activity
```python
activity = ActivityLogService.create_activity(
    user=request.user,
    content_object=incident,
    activity_type=ActivityType.UPDATED
)
# Auto-generates message: "incident updated"
```

### 2. Using the Serializers Directly

#### Creating Activities
```python
from activities.serializers import ActivityLogCreateSerializer
from django.contrib.contenttypes.models import ContentType

data = {
    'user': user.id,
    'activity_type': ActivityType.DOCUMENT_ADDED,
    'description': 'Custom description',
    'content_type': ContentType.objects.get_for_model(incident).id,
    'object_id': incident.id,
    'files': [{'document_url': '...', 'name': 'file.pdf'}]
}

serializer = ActivityLogCreateSerializer(data=data)
if serializer.is_valid():
    activity = serializer.save()
```

#### Listing Activities
```python
from activities.serializers import ActivityLogListSerializer

activities = ActivityLog.objects.filter(
    content_type=content_type,
    object_id=incident_id
).order_by('-timestamp')

serializer = ActivityLogListSerializer(activities, many=True)
return Response({
    'data': serializer.data,
    'count': activities.count()
})
```

### 3. API Response Format

The ActivityLogListSerializer produces responses in this exact format:

```json
{
  "data": [
    {
      "id": 4,
      "incident": 32,
      "activity_message": "2 incoming documents uploaded",
      "activity_highlight": {
        "department": {
          "id": 5,
          "name": "Finance"
        }
      },
      "activity_date": "2023-10-04T16:45:00Z",
      "destination": {
        "id": 5,
        "name": "Finance"
      },
      "files": [
        {
          "document_url": "https://example.com/file.pdf",
          "document_id": "doc123",
          "name": "Report",
          "file_type": "pdf"
        }
      ],
      "created_by": {
        "id": 4,
        "first_name": "Bob",
        "last_name": "Williams"
      },
      "activity_type": "document_added"
    }
  ],
  "count": 1
}
```

### 4. Activity Types Supported

The system supports all ActivityType choices:
- `CREATED`: "incident created"
- `UPDATED`: "incident updated"
- `STATUS_CHANGED`: "status changed"
- `SENT_TO_DEPARTMENT`: "incident sent to [department]"
- `DOCUMENT_ADDED`: "[count] incoming document(s) uploaded"
- `REVIEW_ADDED`: "review added by [user]"
- `ASSIGNED`: "incident assigned to [user]"
- `RESOLVED`: "incident resolved"
- And more...

### 5. Highlight Support

The system supports highlighting different types of objects:

#### Department Highlight
```json
"activity_highlight": {
  "department": {
    "id": 5,
    "name": "Finance"
  }
}
```

#### User Highlight
```json
"activity_highlight": {
  "user": {
    "id": 2,
    "first_name": "Jane",
    "last_name": "Reviewer"
  }
}
```

#### No Highlight
```json
"activity_highlight": {}
```

## Testing

The system includes comprehensive tests covering:

### Serializer Tests (9 tests)
- ActivityLogCreateSerializer validation and creation
- ActivityLogListSerializer response format
- File handling and highlight serialization

### Service Tests (14 tests)
- Activity creation for different types
- Message generation logic
- Content type handling

### View Tests (10 tests)
- API endpoint functionality
- Authentication and authorization
- Response format validation
- Multiple activity scenarios

Run tests with:
```bash
python manage.py test activities.tests.test_new_serializers
python manage.py test activities.tests.test_new_service
python manage.py test activities.tests.test_new_views
```

## Migration and Backward Compatibility

The enhanced system is fully backward compatible with existing code:

1. **Existing Views**: Continue to work with legacy ActivityLogSerializer
2. **Existing Service**: ActivityService methods still function
3. **Database**: Uses the same ActivityLog model with new fields
4. **URLs**: No changes to existing URL patterns

## Best Practices

1. **Use the Service**: Prefer `ActivityLogService.create_activity()` over direct serializer usage
2. **Leverage Auto-generation**: Let the system generate activity messages when possible
3. **Provide Context**: Use highlight_object to provide rich context
4. **Include Files**: When documenting file operations, include file metadata
5. **Test Thoroughly**: Use the provided test patterns for new activity types
