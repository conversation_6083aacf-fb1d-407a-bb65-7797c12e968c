"""
Demo script showing how to use the enhanced activity system.
This script demonstrates the 4 test cases requested:
1. document_added with files
2. sent_to_department with department as destination & highlight
3. review_added with user as highlight
4. updated with no highlight or files
"""

from django.contrib.auth.models import User
from django.contrib.contenttypes.models import ContentType
from activities.models import ActivityType
from activities.services import ActivityLogService
from activities.serializers import ActivityLogListSerializer
from base.models import Department, Facility
from general_patient_visitor.models import GeneralPatientVisitor


def demo_activity_system():
    """Demonstrate the enhanced activity system with all 4 test cases"""
    
    # Setup test data
    user = User.objects.create_user(
        username='demo_user',
        email='<EMAIL>',
        first_name='Demo',
        last_name='User'
    )
    
    reviewer = User.objects.create_user(
        username='reviewer',
        email='<EMAIL>',
        first_name='Jane',
        last_name='Reviewer'
    )
    
    facility = Facility.objects.create(
        name='Demo Facility',
        address='123 Demo St'
    )
    
    department = Department.objects.create(
        name='Finance',
        description='Finance Department',
        facility=facility
    )
    
    incident = GeneralPatientVisitor.objects.create(
        incident_type='General Patient/Visitor',
        status='Draft',
        report_facility=facility,
        department=department,
        created_by=user
    )
    
    print("=== Enhanced Activity System Demo ===\n")
    
    # Test Case 1: document_added with files
    print("1. Document Added Activity:")
    files_data = [
        {
            'document_url': 'https://example.com/report1.pdf',
            'document_id': 'doc123',
            'name': 'Incident Report',
            'file_type': 'pdf'
        },
        {
            'document_url': 'https://example.com/photo1.jpg',
            'document_id': 'doc124',
            'name': 'Evidence Photo',
            'file_type': 'jpg'
        }
    ]
    
    activity1 = ActivityLogService.create_activity(
        user=user,
        content_object=incident,
        activity_type=ActivityType.DOCUMENT_ADDED,
        files=files_data
    )
    
    serializer1 = ActivityLogListSerializer(activity1)
    print(f"   Message: {serializer1.data['activity_message']}")
    print(f"   Files: {len(serializer1.data['files'])} files")
    print(f"   Created by: {serializer1.data['created_by']['first_name']} {serializer1.data['created_by']['last_name']}")
    print()
    
    # Test Case 2: sent_to_department with department highlight
    print("2. Sent to Department Activity:")
    activity2 = ActivityLogService.create_activity(
        user=user,
        content_object=incident,
        activity_type=ActivityType.SENT_TO_DEPARTMENT,
        highlight_object=department
    )
    
    serializer2 = ActivityLogListSerializer(activity2)
    print(f"   Message: {serializer2.data['activity_message']}")
    print(f"   Highlight: {serializer2.data['activity_highlight']}")
    print(f"   Destination: {serializer2.data['destination']}")
    print()
    
    # Test Case 3: review_added with user highlight
    print("3. Review Added Activity:")
    activity3 = ActivityLogService.create_activity(
        user=user,
        content_object=incident,
        activity_type=ActivityType.REVIEW_ADDED,
        highlight_object=reviewer
    )
    
    serializer3 = ActivityLogListSerializer(activity3)
    print(f"   Message: {serializer3.data['activity_message']}")
    print(f"   Highlight: {serializer3.data['activity_highlight']}")
    print(f"   Destination: {serializer3.data['destination']}")
    print()
    
    # Test Case 4: updated with no highlight or files
    print("4. Updated Activity:")
    activity4 = ActivityLogService.create_activity(
        user=user,
        content_object=incident,
        activity_type=ActivityType.UPDATED
    )
    
    serializer4 = ActivityLogListSerializer(activity4)
    print(f"   Message: {serializer4.data['activity_message']}")
    print(f"   Highlight: {serializer4.data['activity_highlight']}")
    print(f"   Files: {serializer4.data['files']}")
    print()
    
    # Show all activities for the incident
    print("=== All Activities for Incident ===")
    from activities.models import ActivityLog
    
    content_type = ContentType.objects.get_for_model(incident)
    all_activities = ActivityLog.objects.filter(
        content_type=content_type,
        object_id=incident.id
    ).order_by('-timestamp')
    
    serializer_all = ActivityLogListSerializer(all_activities, many=True)
    
    for i, activity_data in enumerate(serializer_all.data, 1):
        print(f"{i}. {activity_data['activity_type']}: {activity_data['activity_message']}")
    
    print(f"\nTotal activities: {all_activities.count()}")
    
    # Demonstrate API response format
    print("\n=== Sample API Response Format ===")
    import json
    sample_response = {
        "data": serializer_all.data[:2],  # Show first 2 activities
        "count": all_activities.count()
    }
    print(json.dumps(sample_response, indent=2, default=str))


if __name__ == "__main__":
    # This would be run in a Django shell or management command
    print("Run this in Django shell:")
    print("python manage.py shell")
    print("exec(open('activities/demo_usage.py').read())")
    print("demo_activity_system()")
