from django.db import models
from django.contrib.auth.models import User
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey


class ActivityType(models.TextChoices):
    """Defines the types of activities that can be logged"""
    CREATED = "created", "Created"
    UPDATED = "updated", "Updated"
    STATUS_CHANGED = "status_changed", "Status Changed"
    SENT_TO_DEPARTMENT = "sent_to_department", "Sent to Department"
    SENT_FROM_DEPARTMENT = "sent_from_department", "Sent from Department"
    DOCUMENT_ADDED = "document_added", "Document Added"
    DOCUMENT_REMOVED = "document_removed", "Document Removed"
    REVIEW_ADDED = "review_added", "Review Added"
    ASSIGNED = "assigned", "Assigned"
    UNASSIGNED = "unassigned", "Unassigned"
    RESOLVED = "resolved", "Resolved"
    REOPENED = "reopened", "Reopened"
    INVESTIGATION_STARTED = "investigation_started", "Investigation Started"
    INVESTIGATION_COMPLETED = "investigation_completed", "Investigation Completed"
    COMMENT_ADDED = "comment_added", "Comment Added"
    NOTIFICATION_SENT = "notification_sent", "Notification Sent"
    WORKFLOW_STEP_COMPLETED = "workflow_step_completed", "Workflow Step Completed"
    DELETED = "deleted", "Deleted"


class ActivityLog(models.Model):
    """
    Enhanced activity log model that tracks all activities across different incident types.
    Uses Django's ContentType framework to support any incident model.
    """

    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        related_name="activities",
        null=True,
        blank=True,
    )
    activity_type = models.CharField(max_length=50)
    description = models.TextField(null=True, blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)

    content_type = models.ForeignKey(
        ContentType,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='activity_content_types'
    )
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')

    highlight_content_type = models.ForeignKey(
        ContentType,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='highlight_activities'
    )
    highlight_object_id = models.PositiveIntegerField(null=True, blank=True)
    activity_highlight = GenericForeignKey('highlight_content_type', 'highlight_object_id')

    files = models.JSONField(null=True, blank=True)

    def __str__(self):
        return f"{self.get_activity_type_display()} by {self.user} on {self.timestamp}"
    class Meta:
        ordering = ['-timestamp']
        verbose_name = "Activity Log"
        verbose_name_plural = "Activity Logs"