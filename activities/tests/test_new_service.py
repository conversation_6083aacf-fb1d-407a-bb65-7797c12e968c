from django.test import TestCase
from django.contrib.auth.models import User
from activities.models import ActivityLog, ActivityType
from activities.services import ActivityLogService
from general_patient_visitor.models import GeneralPatientVisitor
from base.models import Department, Facility


class ActivityLogServiceTest(TestCase):
    """Test cases for ActivityLogService"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User'
        )
        
        self.target_user = User.objects.create_user(
            username='targetuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='Target',
            last_name='User'
        )
        
        self.facility = Facility.objects.create(
            name='Test Facility',
            address='123 Test St',
            phone_number='555-0123',
            email='<EMAIL>'
        )
        
        self.department = Department.objects.create(
            name='Finance',
            description='Finance department',
            facility=self.facility
        )
        
        self.incident = GeneralPatientVisitor.objects.create(
            incident_date='2023-10-04',
            incident_time='14:30:00',
            report_facility=self.facility,
            department=self.department,
            created_by=self.user
        )

    def test_create_activity_document_added(self):
        """Test creating document_added activity"""
        files = [
            {
                'document_url': 'https://example.com/file1.pdf',
                'document_id': 'doc123',
                'name': 'Report 1',
                'file_type': 'pdf'
            },
            {
                'document_url': 'https://example.com/file2.pdf',
                'document_id': 'doc124',
                'name': 'Report 2',
                'file_type': 'pdf'
            }
        ]
        
        activity = ActivityLogService.create_activity(
            user=self.user,
            content_object=self.incident,
            activity_type=ActivityType.DOCUMENT_ADDED,
            files=files
        )
        
        self.assertEqual(activity.user, self.user)
        self.assertEqual(activity.activity_type, ActivityType.DOCUMENT_ADDED)
        self.assertEqual(activity.content_object, self.incident)
        self.assertEqual(activity.files, files)
        self.assertEqual(activity.description, '2 incoming documents uploaded')

    def test_create_activity_sent_to_department(self):
        """Test creating sent_to_department activity"""
        activity = ActivityLogService.create_activity(
            user=self.user,
            content_object=self.incident,
            activity_type=ActivityType.SENT_TO_DEPARTMENT,
            highlight_object=self.department
        )
        
        self.assertEqual(activity.user, self.user)
        self.assertEqual(activity.activity_type, ActivityType.SENT_TO_DEPARTMENT)
        self.assertEqual(activity.content_object, self.incident)
        self.assertEqual(activity.activity_highlight, self.department)
        self.assertEqual(activity.description, f'incident sent to {self.department.name}')

    def test_create_activity_review_added(self):
        """Test creating review_added activity"""
        activity = ActivityLogService.create_activity(
            user=self.user,
            content_object=self.incident,
            activity_type=ActivityType.REVIEW_ADDED,
            highlight_object=self.target_user
        )
        
        self.assertEqual(activity.user, self.user)
        self.assertEqual(activity.activity_type, ActivityType.REVIEW_ADDED)
        self.assertEqual(activity.content_object, self.incident)
        self.assertEqual(activity.activity_highlight, self.target_user)
        self.assertEqual(activity.description, f'review added by {self.target_user.first_name} {self.target_user.last_name}')

    def test_create_activity_updated(self):
        """Test creating updated activity"""
        activity = ActivityLogService.create_activity(
            user=self.user,
            content_object=self.incident,
            activity_type=ActivityType.UPDATED
        )
        
        self.assertEqual(activity.user, self.user)
        self.assertEqual(activity.activity_type, ActivityType.UPDATED)
        self.assertEqual(activity.content_object, self.incident)
        self.assertIsNone(activity.activity_highlight)
        self.assertEqual(activity.description, 'incident updated')

    def test_create_activity_with_custom_description(self):
        """Test creating activity with custom description"""
        custom_description = 'Custom activity description'
        
        activity = ActivityLogService.create_activity(
            user=self.user,
            content_object=self.incident,
            activity_type=ActivityType.UPDATED,
            description=custom_description
        )
        
        self.assertEqual(activity.description, custom_description)

    def test_create_activity_without_user(self):
        """Test creating activity without user (system activity)"""
        activity = ActivityLogService.create_activity(
            user=None,
            content_object=self.incident,
            activity_type=ActivityType.UPDATED
        )
        
        self.assertIsNone(activity.user)
        self.assertEqual(activity.activity_type, ActivityType.UPDATED)
        self.assertEqual(activity.content_object, self.incident)

    def test_generate_activity_message_document_added(self):
        """Test activity message generation for document_added"""
        # Test with multiple files
        files = [{'name': 'file1'}, {'name': 'file2'}, {'name': 'file3'}]
        message = ActivityLogService._generate_activity_message(
            ActivityType.DOCUMENT_ADDED, files=files
        )
        self.assertEqual(message, '3 incoming documents uploaded')
        
        # Test with single file
        files = [{'name': 'file1'}]
        message = ActivityLogService._generate_activity_message(
            ActivityType.DOCUMENT_ADDED, files=files
        )
        self.assertEqual(message, '1 incoming document uploaded')
        
        # Test with no files
        message = ActivityLogService._generate_activity_message(
            ActivityType.DOCUMENT_ADDED
        )
        self.assertEqual(message, 'document uploaded')

    def test_generate_activity_message_sent_to_department(self):
        """Test activity message generation for sent_to_department"""
        message = ActivityLogService._generate_activity_message(
            ActivityType.SENT_TO_DEPARTMENT, highlight_object=self.department
        )
        self.assertEqual(message, f'incident sent to {self.department.name}')
        
        # Test without highlight object
        message = ActivityLogService._generate_activity_message(
            ActivityType.SENT_TO_DEPARTMENT
        )
        self.assertEqual(message, 'incident sent to department')

    def test_generate_activity_message_review_added(self):
        """Test activity message generation for review_added"""
        message = ActivityLogService._generate_activity_message(
            ActivityType.REVIEW_ADDED, highlight_object=self.target_user
        )
        self.assertEqual(message, f'review added by {self.target_user.first_name} {self.target_user.last_name}')
        
        # Test without highlight object
        message = ActivityLogService._generate_activity_message(
            ActivityType.REVIEW_ADDED
        )
        self.assertEqual(message, 'review added')

    def test_generate_activity_message_assigned(self):
        """Test activity message generation for assigned"""
        message = ActivityLogService._generate_activity_message(
            ActivityType.ASSIGNED, highlight_object=self.target_user
        )
        self.assertEqual(message, f'incident assigned to {self.target_user.first_name} {self.target_user.last_name}')
        
        # Test without highlight object
        message = ActivityLogService._generate_activity_message(
            ActivityType.ASSIGNED
        )
        self.assertEqual(message, 'incident assigned')

    def test_generate_activity_message_other_types(self):
        """Test activity message generation for other activity types"""
        test_cases = [
            (ActivityType.UPDATED, 'incident updated'),
            (ActivityType.CREATED, 'incident created'),
            (ActivityType.STATUS_CHANGED, 'status changed'),
            (ActivityType.RESOLVED, 'incident resolved'),
        ]
        
        for activity_type, expected_message in test_cases:
            with self.subTest(activity_type=activity_type):
                message = ActivityLogService._generate_activity_message(activity_type)
                self.assertEqual(message, expected_message)

    def test_generate_activity_message_unknown_type(self):
        """Test activity message generation for unknown activity type"""
        message = ActivityLogService._generate_activity_message('unknown_type')
        self.assertEqual(message, 'unknown type performed')

    def test_activity_content_type_and_object_id_set_correctly(self):
        """Test that content_type and object_id are set correctly"""
        activity = ActivityLogService.create_activity(
            user=self.user,
            content_object=self.incident,
            activity_type=ActivityType.UPDATED
        )
        
        self.assertEqual(activity.content_type.model_class(), GeneralPatientVisitor)
        self.assertEqual(activity.object_id, self.incident.id)
        self.assertEqual(activity.content_object, self.incident)

    def test_activity_highlight_content_type_set_correctly(self):
        """Test that highlight content_type and object_id are set correctly"""
        activity = ActivityLogService.create_activity(
            user=self.user,
            content_object=self.incident,
            activity_type=ActivityType.SENT_TO_DEPARTMENT,
            highlight_object=self.department
        )
        
        self.assertEqual(activity.highlight_content_type.model_class(), Department)
        self.assertEqual(activity.highlight_object_id, self.department.id)
        self.assertEqual(activity.activity_highlight, self.department)
