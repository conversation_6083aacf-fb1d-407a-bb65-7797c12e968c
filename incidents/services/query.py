from django.contrib.auth.models import User
from typing import Type
from django.db.models import Model
from accounts.models import Profile
from accounts.services.profile.services import ProfileService
from base.services.department.services import DepartmentService
from base.services.logging.logger import LoggingService
from base.services.permissions.manage_permissions import PermissionsManagement
from base.services.responses import RepositoryResponse
from facilities.services.facility_services import FacilityService
from incidents.services.incident_filter import IncidentFilterService
from incidents.services.query_utils import QueryUtilsServices

logging_service = LoggingService()
facility_service = FacilityService()
department_service = DepartmentService()
profile_service = ProfileService()
permissions_management = PermissionsManagement()
logging_service = LoggingService()
filter_service = IncidentFilterService()
query_utils = QueryUtilsServices()


class IncidentQueryService:
    def __init__(self, user, model: Type[Model], serializer):
        self.model = model
        self.user = user
        self.serializer = serializer

    def get_incidents(
        self,
        user: User,
        model: Type[Model],
        serializer,
        prefetch_fields=[],
        related_fields=[],
        filters={},
    ) -> RepositoryResponse:
        """
        This is a helper function to get incidents
        User it to retrieve incidents, filter them and paginate them
        Filters include:
            1. page: Page number
            2. page_size: Number of incidents to retrieve per page
            3. status: Status of incidents to retrieve
            4. department_id: Department to retrieve incidents for
            5. user_id: User id to retrieve incidents for a specific user
            6. facility_id: Facility to retrieve incidents for specific user
        """
        try:
            # case super user
            incidents = None
            incidents = model.objects.all().prefetch_related(
                *prefetch_fields, *related_fields
            )

            # apply filters
            if filters:
                """Commented [user, model] out for now, as we are not using it yet"""
                incidents = filter_service.apply_filters(
                    incidents=incidents,
                    filters=filters,
                    model=model
                )
            response = query_utils.get_latest_incident_reports(
                incidents,
                serializer,
            )
            if not response.success:
                return RepositoryResponse(
                    data=None,
                    success=False,
                    message="Error retrieving incidents",
                )
            
            return RepositoryResponse(
                data=response.data,
                success=True,
                message="Incidents retrieved successfully",
            )

        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                data=None,
                success=False,
                message="Internal server error",
            )

    def get_incident_by_id(self, incident_id):
        """
        Get incident by id
        """
        try:
            incident = self.model.objects.get(id=incident_id)

            success, serialized_incident, modifications, message = self.get_latest_version(
                incident_data=incident,
                modelName=self.model,
                incidentSerializer=self.serializer,
                incident_id=incident_id,
            )
            serialize_original = self.serializer(incident).data

            if not success:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message=message,
                )

            return RepositoryResponse(
                data={
                    "incident": serialized_incident,
                    "original_incident": serialize_original,
                    "modifications": modifications,
                },
                success=True,
                message="Incident retrieved successfully",
            )

        except self.model.DoesNotExist:
            return RepositoryResponse(
                data=None,
                success=False,
                message="Incident not found",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                data=None,
                success=False,
                message="Internal server error",
            )

    def get_latest_version(
        self, incident_data, modelName, incidentSerializer, incident_id
    ):
        modifications = {}
        incident_latest_version = None
        try:
            if incident_data.versions.count() > 0:
                incident_latest_version = (
                    modelName.objects.get(id=incident_id)
                    .versions.order_by("-created_at")
                    .first()
                )
            else:
                incident_latest_version = incident_data
            incident = incidentSerializer(incident_latest_version).data
            incident["status"] = incident_data.status
            modifications = incident_data.versions.all().order_by("created_at")

            modifications = {
                "count": modifications.count(),
                "versions": [
                    {
                        "date": incident_data.created_at,
                        "id": incident_data.id,
                        "original": True,
                        "latest": incident_data.id == incident_latest_version.id,
                    }
                ]
                + [
                    {
                        "date": modification.created_at,
                        "id": modification.id,
                        "latest": modification.id == incident_latest_version.id,
                        "original": False,
                    }
                    for modification in modifications
                    if modification.id
                ],
            }

            return True, incident, modifications, "success"

        except Exception as e:
            logging_service.log_error(e)
            return False, None, None, "Internal server error"
