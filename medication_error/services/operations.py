from accounts.models import Profile
from accounts.services.user_profile.service import UserProfileService
from medication_error.new_serializers import GetMedicationErrorSerializer
from medication_error.seriaizers import (
    MedicalErrorSerializer,
    MedicationErrorUpdateSerializer,
)
from api.views.auth.permissions_list import (
    is_admin_user,
    is_manager_user,
    is_super_user,
)
from api.views.incidents.general_incident.new_incident import check_anonymous
from base.models import Department
from base.services.forms import check_user_facility
from base.services.logging.logger import LoggingService
from base.services.notifications import save_notification
from base.services.responses import APIResponse
from medication_error.models import MedicationError
from documents.models import Document
from incidents.services.query import IncidentQueryService
from incidents.views.send_to_department import send_incident_submission_email
from reviews.models import Review
from tasks.models import ReviewProcessTasks
from activities.services import ActivityService
from activities.models import ActivityType


user_profile_service = UserProfileService()


class MedicationErrorService:
    def __init__(self, user):
        self.logging_service = LoggingService()
        self.user = user
        self.query_service = IncidentQueryService(
            user=user,
            model=MedicationError,
            serializer=GetMedicationErrorSerializer,
        )

    """A service class for MedicationError model"""

    def get_incident_by_id(self, incident_id) -> APIResponse:
        try:
            incident = self.query_service.get_incident_by_id(
                incident_id=incident_id,
            )
            if not incident.success:
                return APIResponse(
                    success=False,
                    message=incident.message,
                    data=None,
                    code=400,
                )

            return APIResponse(
                success=True,
                message="Medication Error incident retrieved successfully",
                data=incident.data,
                code=200,
            )
        except MedicationError.DoesNotExist:
            return APIResponse(
                success=False,
                message="Medication Error incident not found",
                data=None,
                code=404,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500,
            )

    def get_incidents_list(
        self,
        filters=None,
    ):
        try:
            incidents = self.query_service.get_incidents(
                user=self.user,
                model=MedicationError,
                serializer=GetMedicationErrorSerializer,
                prefetch_fields=[
                    "documents",
                    "reviews",
                    "review_tasks",
                ],
                related_fields=[
                    "patient",
                    "provider_info",
                    "report_facility",
                    "department",
                    "created_by",
                    "review_process",
                ],
                filters=filters,
            )
            if not incidents.success:
                return APIResponse(
                    success=False,
                    message=incidents.message,
                    data=None,
                    code=400,
                )

            return APIResponse(
                success=True,
                message="Medication Error incidents retrieved successfully",
                data=incidents.data,
                code=200,
            )
        except Exception as e:
            LoggingService().log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500,
            )

    def create_incident(self, data) -> APIResponse:
        try:
            facility_response = check_user_facility(data, self.user)
            if not facility_response.success:
                return APIResponse(
                    success=False,
                    message=facility_response.message,
                    data=None,
                    code=400,
                )
            facility = facility_response.data

            request_data = data.copy()
            document_ids = request_data.pop("documents", [])
            review_ids = request_data.pop("reviews", [])
            review_task_ids = request_data.pop("review_tasks", [])

            # Handle 'patient' field for UserProfile
            if "patient" in data:
                patient_profile = user_profile_service.get_or_create_profile(
                    data["patient"]
                )
                if not patient_profile.success:
                    return APIResponse(
                        success=False,
                        message=patient_profile.message,
                        data=None,
                        code=400,
                    )
                request_data["patient"] = patient_profile.data.id

            # Handle 'provider_info' field for UserProfile
            if "provider_info" in data:
                provider_profile = user_profile_service.get_or_create_profile(
                    data["provider_info"]
                )
                if not provider_profile.success:
                    return APIResponse(
                        success=False,
                        message=provider_profile.message,
                        data=None,
                        code=400,
                    )
                request_data["provider_info"] = provider_profile.data.id

            request_data["created_by"] = self.user.id
            request_data["report_facility"] = facility
            serializer = MedicalErrorSerializer(data=request_data)

            if serializer.is_valid():
                instance = serializer.save()
                if document_ids:
                    instance.documents.set(Document.objects.filter(id__in=document_ids))
                if review_ids:
                    instance.reviews.set(Review.objects.filter(id__in=review_ids))
                if review_task_ids:
                    instance.review_tasks.set(
                        ReviewProcessTasks.objects.filter(id__in=review_task_ids)
                    )

                # save notification and assign it to quality managers
                save_notification(
                    facility=serializer.data["report_facility"],
                    group_name="Admin",
                    notification_type="info",
                    notification_category="incident",
                    message="A new Medication Error incident is submitted",
                    item_id=serializer.data["id"],
                )

                ActivityService.log_creation(
                    user=self.user,
                    content_object=instance,
                    facility=facility,
                    details={
                        'incident_type': 'Medication Error',
                        'status': instance.status,
                    }
                )

                if document_ids:
                    ActivityService.log_document_activity(
                        user=self.user,
                        content_object=instance,
                        document_count=len(document_ids),
                        is_addition=True,
                        details={'incident_type': 'Medication Error'}
                    )

                return APIResponse(
                    success=True,
                    message="Medication Error incident created successfully",
                    data=serializer.data,
                    code=201,
                )
            self.logging_service.log_error(serializer.errors)
            # invalid data error can be logged or handled here if needed
            return APIResponse(
                success=False,
                message="Invalid data",
                data=None,
                code=400,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500,
            )

    def update_incident(self, id, data) -> APIResponse:
        try:
            request_data = data.copy()
            medication_error = MedicationError.objects.get(id=id)
            profile = Profile.objects.get(user=self.user)
            if (
                not is_super_user(self.user)
                and not is_admin_user(self.user, profile.facility)
                and not is_manager_user(self.user, medication_error.department)
                and not self.user == medication_error.created_by
            ):
                return APIResponse(
                    success=False,
                    message="You do not have enough rights to update this incident",
                    data=None,
                    code=403,
                )
            document_ids = request_data.pop("documents", None)
            review_ids = request_data.pop("reviews", None)
            review_task_ids = request_data.pop("review_tasks", None)
            facility = medication_error.report_facility
            request_data["report_facility"] = facility

            # Handle 'patient' field for UserProfile
            if "patient" in data:
                patient_profile = user_profile_service.get_or_create_profile(
                    data["patient"]
                )
                if not patient_profile.success:
                    return APIResponse(
                        success=False,
                        message=patient_profile.message,
                        data=None,
                        code=400,
                    )
                request_data["patient"] = patient_profile.data.id

            # Handle 'provider_info' field for UserProfile
            if "provider_info" in data:
                provider_profile = user_profile_service.get_or_create_profile(
                    data["provider_info"]
                )
                if not provider_profile.success:
                    return APIResponse(
                        success=False,
                        message=provider_profile.message,
                        data=None,
                        code=400,
                    )
                request_data["provider_info"] = provider_profile.data.id

            if "report_facility" in data:
                facility_response = self._process_facility(data["report_facility"])
                if not facility_response.success:
                    return APIResponse(
                        success=False,
                        message=facility_response.message,
                        code=400,
                    )
                facility = facility_response.data
                request_data["report_facility"] = facility

            if "department" in data:
                try:
                    department_obj = Department.objects.get(id=data["department"])
                    request_data["department"] = department_obj.id
                except Department.DoesNotExist:
                    return APIResponse(
                        success=False,
                        message="Department not found",
                        code=400,
                        data=None,
                    )
                except Exception as dept_e:
                    self.logging_service.log_error(dept_e)
                    return APIResponse(
                        success=False,
                        message="Error processing department",
                        code=500,
                        data=None,
                    )

            data = check_anonymous(request_data, self.user)
            serializer = MedicationErrorUpdateSerializer(
                medication_error, data=data, partial=True
            )
            if serializer.is_valid():
                serializer.save()
                if document_ids is not None:
                    medication_error.documents.set(  # Changed model
                        Document.objects.filter(id__in=document_ids)
                    )
                if review_ids is not None:
                    medication_error.reviews.set(
                        Review.objects.filter(id__in=review_ids)
                    )

                if review_task_ids is not None:
                    medication_error.review_tasks.set(
                        ReviewProcessTasks.objects.filter(id__in=review_task_ids)
                    )
                if "status" in data and data.get("status") == "Open":
                    send_incident_submission_email(
                        incident=medication_error,
                        incident_type=data.get("incident_type", "Medication Error"),
                    )
                ActivityService.log_activity(
                    user=self.user,
                    content_object=medication_error,
                    activity_type=ActivityType.UPDATED,
                    description="Incident modified",
                    details={
                        'incident_type': 'Medication Error',
                        'modification_type': 'incident_update'
                    }
                )
                serialized_data = GetMedicationErrorSerializer(medication_error)
                return APIResponse(
                    success=True,
                    message="Medication Error incident updated successfully",
                    data=serialized_data.data,
                    code=200,
                )
            else:
                self.logging_service.log_error(serializer.errors)
                return APIResponse(
                    success=False,
                    message=f"Invalid data: {serializer.errors}",
                    data=None,
                    code=400,
                )
        except Profile.DoesNotExist:
            return APIResponse(
                success=False,
                message="User profile not found",
                data=None,
                code=404,
            )
        except MedicationError.DoesNotExist:
            return APIResponse(
                success=False,
                message="Medication Error incident not found",
                data=None,
                code=404,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500,
            )
